// TabBar 配置（参考微信官方最佳实践）
const TABBAR_CONFIGS = {
  '1': [ // 业主
    {
      value: 0,
      show: true,
      text: "首页",
      icon: "wap-home-o",
      pagePath: "/pages/index/index"
    }, {
      value: 1,
      show: true,
      text: "信息公开",
      icon: "bullhorn-o",
      pagePath: "/pages/service/index"
    }, {
      value: 2,
      show: true,
      text: "收支公示",
      icon: "balance-list-o",
      pagePath: "/pages/finance/finance"
    }, {
      value: 3,
      show: true,
      text: "我的",
      icon: "user-o",
      pagePath: "/pages/mine/index"
    }
  ],
  '2': [ // 物业
    {
      value: 0,
      show: true,
      text: "工作台",
      icon: "dashboard-o",
      pagePath: "/pages/property/dashboard/index"
    }, {
      value: 1,
      show: true,
      text: "工单",
      icon: "orders-o",
      pagePath: "/pages/property/orders/index"
    }, {
      value: 2,
      show: true,
      text: "巡检",
      icon: "check-o",
      pagePath: "/pages/property/patrol/index"
    }, {
      value: 3,
      show: true,
      text: "我的",
      icon: "user-o",
      pagePath: "/pages/property/profile/index"
    }
  ]
}

Component({
  data: {
    active: -1,
    list: TABBAR_CONFIGS['1'] // 默认业主配置
  },

  methods: {
    onChange(event) {
      const { value } = event.detail
      const { list } = this.data
      console.log('[TabBar] 切换到:', value)

      this.setData({
        active: value
      })

      wx.switchTab({
        url: list[value].pagePath
      })
    },

    /**
     * 初始化TabBar（官方推荐方式）
     * 在每个TabBar页面的onShow中调用
     */
    init() {
      const page = getCurrentPages().pop()
      const urls = this.data.list.map(v => v.pagePath)
      const active = urls.findIndex(v => v === `/${page.route}`)

      console.log('[TabBar] 初始化:', { route: page.route, active })

      this.setData({
        active
      })
    },

    /**
     * 根据用户类型切换TabBar（官方推荐方式）
     * @param {string} userType 用户类型 '1'=业主, '2'=物业
     */
    toggleMenu(userType) {
      console.log('[TabBar] 切换菜单:', userType)

      try {
        const newList = TABBAR_CONFIGS[userType] || TABBAR_CONFIGS['1']

        this.setData({
          list: newList
        })

        // 重新计算当前选中状态
        this.init()

        console.log('[TabBar] 菜单切换完成:', { userType, listLength: newList.length })
      } catch (error) {
        console.error('[TabBar] 切换菜单失败:', error)
      }
    }
  }
})