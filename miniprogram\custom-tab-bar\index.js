import { getStateManager } from '../utils/stateManager.js'

// TabBar 配置
const TABBAR_CONFIGS = {
  '1': [ // 业主
    {
      pagePath: "/pages/index/index",
      text: "首页",
      icon: "wap-home-o"
    }, {
      pagePath: "/pages/service/index",
      text: "信息公开",
      icon: "bullhorn-o"
    }, {
      pagePath: "/pages/finance/finance",
      text: "收支公示",
      icon: "balance-list-o"
    }, {
      pagePath: "/pages/mine/index",
      text: "我的",
      icon: "user-o"
    }
  ],
  '2': [ // 物业
    {
      pagePath: "/pages/property/dashboard/index",
      text: "工作台",
      icon: "dashboard-o"
    }, {
      pagePath: "/pages/property/orders/index",
      text: "工单",
      icon: "orders-o"
    }, {
      pagePath: "/pages/property/patrol/index",
      text: "巡检",
      icon: "check-o"
    }, {
      pagePath: "/pages/property/profile/index",
      text: "我的",
      icon: "user-o"
    }
  ]
}

Component({
  data: {
    selected: 0,
    list: TABBAR_CONFIGS['1'], // 默认业主配置
    currentUserType: '1', // 缓存当前用户类型
    isUpdating: false // 防止并发更新
  },

  lifetimes: {
    attached() {
      this.initTabBar()
    }
  },

  methods: {
    onChange(event) {
      const index = event.detail
      const url = this.data.list[index].pagePath
      wx.switchTab({
        url
      })
    },

    /**
     * 初始化TabBar（只在组件创建时调用一次）
     */
    initTabBar() {
      // 防止并发初始化
      if (this.data.isUpdating) {
        console.log('[TabBar] 正在更新中，跳过初始化')
        return
      }

      try {
        this.setData({ isUpdating: true })

        const stateManager = getStateManager()
        const state = stateManager.getState()
        const userType = state.userType || '1'

        this.updateTabBarConfig(userType)
      } catch (error) {
        console.error('初始化TabBar失败:', error)
      } finally {
        // 延迟重置更新状态，避免快速连续调用
        setTimeout(() => {
          this.setData({ isUpdating: false })
        }, 100)
      }
    },

    /**
     * 更新TabBar配置（只在用户类型变化时调用）
     */
    updateTabBarConfig(userType) {
      // 防止重复更新
      if (this.data.currentUserType === userType) {
        console.log('[TabBar] 用户类型未变化，跳过更新:', userType)
        return
      }

      // 防止并发更新
      if (this.data.isUpdating) {
        console.log('[TabBar] 正在更新中，跳过配置更新')
        return
      }

      try {
        const newList = TABBAR_CONFIGS[userType] || TABBAR_CONFIGS['1']

        this.setData({
          list: newList,
          currentUserType: userType
        })

        console.log('[TabBar] 配置已更新:', { userType, listLength: newList.length })
      } catch (error) {
        console.error('[TabBar] 更新配置失败:', error)
      }
    },

    /**
     * 根据用户类型更新TabBar配置（兼容旧接口）
     */
    updateTabBarByUserType() {
      try {
        const stateManager = getStateManager()
        const state = stateManager.getState()
        const userType = state.userType || '1'

        this.updateTabBarConfig(userType)
      } catch (error) {
        console.error('更新TabBar配置失败:', error)
      }
    },

    /**
     * 设置当前选中的tab
     */
    setSelected(index) {
      // 防止重复设置相同的选中状态
      if (this.data.selected === index) {
        return
      }

      this.setData({
        selected: index
      })
    },

    /**
     * 根据页面路径获取TabBar索引
     * @param {string} pagePath 页面路径
     * @returns {number} 索引，未找到返回-1
     */
    getIndexByPath(pagePath) {
      const list = this.data.list || []
      for (let i = 0; i < list.length; i++) {
        if (list[i].pagePath === pagePath) {
          return i
        }
      }
      return -1
    }
  }
})