# TabBar使用指南

## 概述

基于微信官方最佳实践，我们的TabBar系统采用了统一管理器模式，同时保持与官方推荐用法的兼容性。

## 核心组件

### 1. TabBar管理器
**文件**: `miniprogram/utils/tabBarManager.js`
- 统一管理TabBar配置和状态
- 自动处理用户类型切换
- 支持配置项过滤

### 2. TabBar组件
**文件**: `miniprogram/custom-tab-bar/index.js`
- 符合官方最佳实践
- 支持权限控制
- 自动同步选中状态

## 使用方法

### 在TabBar页面中使用

每个TabBar页面都需要在 `onShow` 中调用TabBar的 `init()` 方法：

```javascript
// pages/index/index.js
Page({
  onShow() {
    // 官方推荐：每个TabBar页面都要调用
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().init()
    }
  }
})
```

### 根据权限切换TabBar

在登录成功或权限变化时，调用 `toggleMenu()` 方法：

```javascript
// 登录成功后
const tabBar = this.getTabBar()
if (tabBar) {
  tabBar.toggleMenu(userType) // '1' 业主, '2' 物业
}

// 或者使用TabBar管理器
import { getTabBarManager } from '../utils/tabBarManager.js'
const tabBarManager = getTabBarManager()
tabBarManager.updateUserType(userType)
tabBarManager.forceRefresh()
```

## 配置说明

### TabBar配置结构

```javascript
const BASE_TABBAR_CONFIGS = {
  '1': [ // 业主配置
    {
      pagePath: "/pages/index/index",
      text: "首页",
      icon: "wap-home-o",
      required: true // 必需项，始终显示
    },
    {
      pagePath: "/pages/markicam/index",
      text: "工作照片",
      icon: "photo-o",
      configKey: "enable_markicam" // 配置控制项
    }
  ],
  '2': [ // 物业配置
    {
      pagePath: "/pages/property/dashboard/index",
      text: "工作台",
      icon: "wap-home-o",
      required: true
    }
  ]
}
```

### 配置字段说明

- `pagePath`: 页面路径
- `text`: 显示文本
- `icon`: 图标名称
- `required`: 是否必需项（true=始终显示）
- `configKey`: 配置控制键（可选，用于动态控制显示）

## 最佳实践

### 1. 页面初始化

```javascript
Page({
  onShow() {
    // 必须：初始化TabBar
    this.getTabBar()?.init()
    
    // 可选：物业页面额外验证
    if (this.isPropertyPage) {
      const userType = getCurrentUserType()
      if (userType !== '2') {
        wx.switchTab({ url: '/pages/index/index' })
        return
      }
    }
  }
})
```

### 2. 登录后更新

```javascript
// 登录服务中
loginSuccess(loginData) {
  // 更新状态
  stateManager.setLoginSuccess(loginData)
  
  // 更新TabBar
  const tabBarManager = getTabBarManager()
  tabBarManager.updateUserType(loginData.userType)
  tabBarManager.forceRefresh()
}
```

### 3. 权限控制

```javascript
// 根据权限动态调整TabBar
function updateTabBarByPermission(permissions) {
  const tabBar = getCurrentPages().pop()?.getTabBar()
  if (tabBar) {
    // 方法1：使用toggleMenu
    tabBar.toggleMenu(userType)
    
    // 方法2：使用管理器
    const tabBarManager = getTabBarManager()
    tabBarManager.updateUserType(userType)
    tabBarManager.forceRefresh()
  }
}
```

## 常见问题

### Q1: TabBar显示不正确
**解决方案**: 确保每个TabBar页面的 `onShow` 中都调用了 `this.getTabBar().init()`

### Q2: 用户类型切换后TabBar没更新
**解决方案**: 在用户类型变化后调用 `toggleMenu(userType)` 或使用TabBar管理器强制刷新

### Q3: 配置项不生效
**解决方案**: 检查 `configManager` 是否正确初始化，确保配置键名正确

## API参考

### TabBar组件方法

```javascript
// 初始化TabBar（页面onShow时调用）
this.getTabBar().init()

// 根据权限切换菜单
this.getTabBar().toggleMenu(userType)

// 更新配置（兼容旧版本）
this.getTabBar().updateTabBarConfig(userType)

// 强制重新初始化（兼容旧版本）
this.getTabBar().forceReinit(userType)
```

### TabBar管理器方法

```javascript
import { getTabBarManager } from '../utils/tabBarManager.js'

const tabBarManager = getTabBarManager()

// 初始化管理器
tabBarManager.init()

// 更新用户类型
tabBarManager.updateUserType(userType)

// 强制刷新所有TabBar
tabBarManager.forceRefresh()

// 更新指定页面的TabBar
tabBarManager.updatePageTabBar(page)

// 获取当前用户类型
const currentUserType = tabBarManager.getCurrentUserType()
```

## 注意事项

1. **必须调用init()**: 每个TabBar页面的 `onShow` 中都必须调用 `this.getTabBar().init()`
2. **用户类型**: '1' 表示业主，'2' 表示物业
3. **配置更新**: 配置变化后需要调用相应的更新方法
4. **错误处理**: 所有TabBar操作都有错误处理，失败时会使用默认配置
5. **性能优化**: TabBar管理器使用单例模式，避免重复初始化
