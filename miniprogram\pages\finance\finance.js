import { getStateManager } from '../../utils/stateManager.js'
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'
import { formatTime, debounce } from '../../utils/common.js'
import { getSystemInfoSyncCompat } from '../../utils/systemInfoCompat.js'

const app = getApp()
const stateManager = getStateManager()
const loadingManager = getLoadingManager()

Page({
  data: {
    // 基础数据
    years: [],
    currentYear: '',

    // 年度数据
    yearData: [],

    // 当前显示的年度详情
    currentYearDetail: null,

    // 账户信息
    accountInfo: {
      balance: '',
      updateTime: '',
      dataSource: '',
      accountNumber: ''
    },

    // 页面状态
    loading: false,
    refreshing: false,

    // Vant Collapse 状态管理
    activeYears: [], // 展开的年份索引数组
    activeMonths: {}, // 每个年份下展开的月份索引 {yearIndex: [monthIndex]}

    // 页面UI状态
    statusBarHeight: 0,
    communityInfo: {
      communityName: '智慧小区'
    }
  },

  onLoad() {
    // 获取状态栏高度
    const systemInfo = getSystemInfoSyncCompat()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    })
    this.checkLoginAndInit()
  },

  async onShow() {
    this.setTabBarSelected()

    // 检查是否需要刷新数据
    if (stateManager.checkAndClearRefresh()) {
      console.log('[Finance] 检测到全局刷新标记，重新获取数据')
      await this.refreshData()
    }

    this.updatePageState()
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  // 检查登录状态并初始化
  checkLoginAndInit() {
    if (!this.checkLoginStatus()) return
    this.initializePage()
  },

  // 设置 TabBar（按照微信官方最佳实践）
  setTabBarSelected() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      const tabBar = this.getTabBar()
      // 切换到业主菜单
      if (typeof tabBar.toggleMenu === 'function') {
        tabBar.toggleMenu('1')
      }
      // 初始化选中状态
      if (typeof tabBar.init === 'function') {
        tabBar.init()
      }
    }
  },

  // 更新页面状态
  updatePageState() {
    try {
      const state = stateManager.getState()
      this.setData({
        communityInfo: state.communityInfo || this.data.communityInfo
      })
    } catch (error) {
      console.warn('[Finance] 更新页面状态失败:', error)
    }
  },

  // 初始化页面
  async initializePage() {
    await this.loadYears()
  },

  // 刷新数据
  async refreshData() {
    this.setData({ refreshing: true })
    
    try {
      await this.loadYears()
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      handleError(error, '刷新数据')
    } finally {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    }
  },

  // 加载年份列表
  async loadYears() {
    try {
      loadingManager.show('加载中...')
      
      const res = await app.request({
        url: '/api/wx/ccbv2/years',
        method: 'GET'
      })
      
      if (res.code === 0) {
        const years = res.data.years || []
        const currentYear = years[0] || ''
        
        this.setData({
          years,
          currentYear,
          accountInfo: {
            balance: res.data.balance || '--',
            updateTime: res.data.updateTime || '',
            dataSource: res.data.dataSource || '建设银行',
            accountNumber: res.data.accountNumber || '--'
          }
        })

        // 初始化年度数据
        const yearData = years.map((year, index) => ({
          year,
          expanded: index === 0, // 默认展开第一年
          yearIncome: '--',
          yearExpense: '--',
          months: [],
          loading: false,
          loaded: false
        }))

        // 初始化Vant Collapse状态 - 默认展开第一年
        const activeYears = years.length > 0 ? [0] : []
        const activeMonths = {}

        this.setData({
          yearData,
          activeYears,
          activeMonths
        })

        // 自动加载第一年的数据
        if (currentYear) {
          await this.loadYearDetail(currentYear, 0)
        }
      } else {
        throw new Error(res.msg || '获取年份列表失败')
      }
    } catch (error) {
      handleError(error, '获取年份列表')
    } finally {
      loadingManager.hide()
    }
  },

  // 加载年度详情
  async loadYearDetail(year, yearIndex) {
    const yearData = [...this.data.yearData]
    
    // 如果已经加载过，直接返回
    if (yearData[yearIndex].loaded) {
      return
    }

    try {
      // 设置加载状态
      yearData[yearIndex].loading = true
      this.setData({ yearData })

      const res = await app.request({
        url: '/api/wx/ccbv2/yearDetail',
        method: 'GET',
        data: { year }
      })

      if (res.code === 0) {
        const detail = res.data
        
        // 处理月份数据
        const months = (detail.months || []).map(month => ({
          ...month,
          expanded: false,
          loading: false,
          details: []
        }))

        // 更新年度数据
        yearData[yearIndex] = {
          ...yearData[yearIndex],
          yearIncome: detail.yearIncome || '0.00',
          yearExpense: detail.yearExpense || '0.00',
          months,
          loading: false,
          loaded: true
        }

        this.setData({ yearData })
      } else {
        throw new Error(res.msg || '获取年度详情失败')
      }
    } catch (error) {
      // 恢复加载状态
      yearData[yearIndex].loading = false
      this.setData({ yearData })
      
      handleError(error, '获取年度详情')
    }
  },

  // Vant Collapse年份变化事件
  async onYearChange(e) {
    const activeYears = e.detail

    // 确保activeYears是数组
    const activeYearsArray = Array.isArray(activeYears) ? activeYears :
                             (typeof activeYears === 'string' || typeof activeYears === 'number') ? [activeYears] : []

    // 更新年度数据的expanded状态
    const yearData = [...this.data.yearData]
    yearData.forEach((item, index) => {
      item.expanded = activeYearsArray.includes(index)
    })

    this.setData({
      activeYears: activeYearsArray,
      yearData
    })

    // 加载新展开年份的数据
    for (const yearIndex of activeYearsArray) {
      const targetYear = yearData[yearIndex]
      if (targetYear && !targetYear.loaded) {
        await this.loadYearDetail(targetYear.year, yearIndex)
      }
    }

    // 清理收起年份的月份展开状态
    const activeMonths = { ...this.data.activeMonths }
    Object.keys(activeMonths).forEach(yearIndex => {
      if (!activeYearsArray.includes(parseInt(yearIndex))) {
        delete activeMonths[yearIndex]
      }
    })
    this.setData({ activeMonths })
  },

  // 切换月份展开状态
  toggleMonth: debounce(async function(e) {
    const year = e.currentTarget.dataset.year
    const month = e.currentTarget.dataset.month
    const yearIndex = e.currentTarget.dataset.yearIndex
    const monthIndex = e.currentTarget.dataset.monthIndex

    const yearData = [...this.data.yearData]
    const months = [...yearData[yearIndex].months]
    const targetMonth = months[monthIndex]

    // 如果当前月份已展开，则收起
    if (targetMonth.expanded) {
      months[monthIndex].expanded = false
      months[monthIndex].details = []
    } else {
      // 收起其他月份，展开当前月份
      months.forEach((item, index) => {
        item.expanded = index === monthIndex
        if (index !== monthIndex) {
          item.details = []
        }
      })

      // 加载月份详情
      await this.loadMonthDetail(month, yearIndex, monthIndex)
    }

    yearData[yearIndex].months = months
    this.setData({ yearData })
  }, 300),



  // 加载月份详情
  async loadMonthDetail(month, yearIndex, monthIndex) {
    const yearData = [...this.data.yearData]
    const months = [...yearData[yearIndex].months]
    
    try {
      // 设置加载状态
      months[monthIndex].loading = true
      yearData[yearIndex].months = months
      this.setData({ yearData })

      const res = await app.request({
        url: '/api/wx/ccbv2/monthDetail',
        method: 'GET',
        data: { month }
      })

      if (res.code === 0) {
        // 处理交易详情数据
        const details = (res.data.list || []).map(item => ({
          ...item,
          amount: this.formatAmount(item.amount),
          date: formatTime(item.date, 'MM-DD')
        }))

        months[monthIndex].details = details
        months[monthIndex].loading = false
        months[monthIndex].monthIncome = res.data.monthIncome || '0.00'
        months[monthIndex].monthExpense = res.data.monthExpense || '0.00'
      } else {
        throw new Error(res.msg || '获取月份详情失败')
      }
    } catch (error) {
      months[monthIndex].loading = false
      months[monthIndex].details = []
      handleError(error, '获取月份详情')
    }
    
    yearData[yearIndex].months = months
    this.setData({ yearData })
  },

  // 查看交易详情
  viewTransactionDetail(e) {
    const id = e.currentTarget.dataset.id
    if (!id) {
      wx.showToast({
        title: '交易ID无效',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: `/pages/finance/detail?id=${id}`
    })
  },

  // 格式化金额
  formatAmount(amount) {
    if (!amount) return '0.00'
    const num = parseFloat(amount)
    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  },

  // 跳转到房屋列表
  goToHouseList() {
    wx.navigateTo({
      url: '/pages/house/index'
    })
  }
})