// index.js
import { getStateManager } from '../../utils/stateManager.js'
import { handleError } from '../../utils/errorHandler.js'

import { getWatermarkManager } from '../../utils/watermarkManager.js'
import requestManager from '../../utils/requestManager.js'
import { getSystemInfoSyncCompat } from '../../utils/systemInfoCompat.js'
import subscribeManager, { TEMPLATE_TYPES } from '../../utils/subscribeManager.js'
import { UIConfig, ShareConfig } from '../../utils/configHelper.js'
import { getIndexCacheManager, CACHE_TYPES } from '../../utils/indexCacheManager.js'
import { getIndexDataService } from '../../services/indexDataService.js'
import { createIndexUIManager } from '../../managers/indexUIManager.js'
import { createIndexEventHandler } from '../../handlers/indexEventHandler.js'
import {
  DEFAULT_MAIN_CARDS,
  MENU_ICON_COLORS,
  STORAGE_KEYS
} from '../../constants/indexConstants.js'

const app = getApp()
const stateManager = getStateManager()
const watermarkManager = getWatermarkManager()
const cacheManager = getIndexCacheManager()
const dataService = getIndexDataService()



Page({
  data: {
    isLogin: false,
    userInfo: null,
    ownerInfo: null,
    isHouseAuth: false,
    houseInfo: null,
    communityInfo: {
      communityName: '睦邻共治',
      communityBanner: '/static/images/banner.png'
    },
    newsList: [],
    menuList: [],
    menuRows: [],
    // 动态主卡片数据
    dynamicMainCards: [],
    latestNotice: null,
    currentNoticeType: '',
    categoryNewsList: [],
    noticeTypes: [],
    hasMoreNews: false,
    // 系统信息
    statusBarHeight: 20,
    // 水印相关
    showWatermark: false,
    watermarkConfig: null,
    // 页面loading状态
    pageLoading: false,
    contentVisible: true,
    // 功能容器显示状态
    functionContainerVisible: false,
    // 添加到桌面引导
    showDesktopGuide: false,
    // 配置相关数据
    showBigCard: true,
    enableMiniprogramTitle: false,
    miniprogramTitle: '',
    miniprogramSubtitle: ''
  },

  async onLoad() {

  // 获取系统信息
    this.getSystemInfo()

    // 初始化UI管理器
    this.uiManager = createIndexUIManager(this)

    // 初始化事件处理器
    this.eventHandler = createIndexEventHandler(this, dataService)

  
    // 初始化默认主卡片数据
    this.setData({
      dynamicMainCards: [...DEFAULT_MAIN_CARDS]
    })
    await this.initializePage()
    // 检测分享来源
    this.checkShareSource()
  },

  async onShow() {
    // 防止重复执行，但增加安全检查
    if (this.isOnShowRunning) {
      console.warn('[Index] onShow已在执行中，跳过本次调用')
      return
    }
    this.isOnShowRunning = true

    try {
      // 确保UI管理器已初始化
      if (!this.uiManager) {
        console.warn('[Index] UI管理器未初始化，等待初始化完成')
        // 等待一小段时间让onLoad完成
        await new Promise(resolve => setTimeout(resolve, 100))

        // 如果还是没有初始化，重新创建
        if (!this.uiManager) {
          console.log('[Index] 重新创建UI管理器')
          this.uiManager = createIndexUIManager(this)
        }
      }

      // 设置tabBar（按照微信官方最佳实践）
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        const tabBar = this.getTabBar()
        // 切换到业主菜单
        if (typeof tabBar.toggleMenu === 'function') {
          tabBar.toggleMenu('1')
        }
        // 初始化选中状态
        if (typeof tabBar.init === 'function') {
          tabBar.init()
        }
      }

      // 刷新页面状态
      this.refreshPageState()

      // 检查用户是否变化，如果变化则清除缓存
      this.checkUserChange()

      // 简化的登录检查，不阻塞页面
      const token = wx.getStorageSync('token')
      const userInfo = wx.getStorageSync('wxUserInfo')

      if (token && userInfo?.nickName) {
        // 有登录状态，检查是否需要刷新数据
        const needsRefresh = stateManager.checkAndClearRefresh()
        if (needsRefresh) {
          console.log('[Index] 检测到全局刷新标记，重新获取数据')
          this.clearAllCache() // 清除缓存强制刷新
          this.loadPageDataAsync()
        } else {
          // 智能加载：只加载缓存过期的数据
          this.loadPageDataSmartly()
        }
      }

    } catch (error) {
      console.error('[Index] onShow执行失败:', error)
      // 出错时强制显示内容，避免一直loading
      if (this.uiManager) {
        this.uiManager.forceShowContent()
      }
    } finally {
      this.isOnShowRunning = false
    }
  },

  // 下拉刷新
  async onPullDownRefresh() {
    try {

      this.setData({
        contentVisible: true
      })

      // 清除本页面缓存
      this.clearAllCache()

      // 清除全局请求缓存，影响其他页面
      requestManager.clearCache()

      // 设置全局刷新标记，通知其他页面需要刷新
      stateManager.setNeedsRefresh(true)

      // 重新获取数据
      await Promise.all([
        this.checkAndUpdateLoginStatus(),
        this.getMenuList(),
        this.getNoticeTypes() // 这个方法内部会调用getNewsList
      ])

      // 重新设置菜单样式
      this.setMenuIconColors()
      this.setMenuRows()

      // 刷新页面数据
      await this.refreshPageData()

      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })

    } catch (error) {
      console.warn('[Index] 下拉刷新失败:', error.message || error)
      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none',
        duration: 2000
      })
    } finally {
      // 停止下拉刷新动画
      wx.stopPullDownRefresh()
    }
  },

  // 清除所有缓存
  clearAllCache() {
    cacheManager.clear()
  },

  // 初始化页面
  async initializePage() {
    await this.uiManager.createProtectedInitialization(
      () => this.performInitialization(),
      3000
    )
  },



  // 执行初始化逻辑
  async performInitialization() {
    // 检查登录状态
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('wxUserInfo')

    console.log('[Index] 初始化检查登录状态:', { hasToken: !!token, hasUserInfo: !!userInfo?.nickName })

    if (token && userInfo?.nickName) {
      // 有登录状态，立即显示首页内容
      console.log('[Index] 检测到登录状态，立即显示页面内容')

      stateManager.setState({
        isLogin: true,
        userInfo
      })

      // 立即显示页面内容
      this.uiManager.showContent()

      // 更新页面数据
      await this.refreshPageData()

      // 异步加载其他数据，不阻塞页面显示
      this.loadPageDataAsync()

      // 检查是否需要显示添加到桌面引导
      this.checkDesktopGuide()
    } else {
      // 没有登录状态，检查登录但不阻塞页面显示
      console.log('[Index] 未检测到登录状态，显示基础内容')

      // 先显示页面内容
      this.uiManager.showContent()

      // 异步检查登录状态，不等待结果
      this.checkLoginStatus()

      // 尝试加载基础数据（如菜单等），即使未登录也可以显示
      this.loadPageDataAsync().catch(error => {
        console.warn('[Index] 加载基础数据失败:', error)
      })
    }
  },

  // 异步加载页面数据（不阻塞页面显示）
  async loadPageDataAsync() {
    try {
      // 并行加载数据，失败不影响页面显示
      await Promise.allSettled([
        this.getMenuList(),
        this.getNoticeTypes(), // 这个方法内部会调用getNewsList
        this.checkAuthenticationStatus()
      ])

      // 设置菜单图标颜色和行布局
      this.setMenuIconColors()
      this.setMenuRows()
    } catch (error) {
      console.warn('[Index] 异步加载数据失败:', error)
    }
  },

  // 智能加载页面数据（只加载缓存过期的数据）
  async loadPageDataSmartly() {
    try {
      const loadTasks = []

      // 新闻数据现在通过getNoticeTypes方法加载，这里不需要单独检查

      // 检查菜单缓存
      if (!cacheManager.has(CACHE_TYPES.MENU, 'indexNav')) {
        loadTasks.push(this.getMenuList())
      }

      // 检查字典缓存
      if (!cacheManager.has(CACHE_TYPES.DICT, 'sys_notice_type')) {
        loadTasks.push(this.getNoticeTypes())
      }

      // 检查认证状态缓存
      if (!cacheManager.has(CACHE_TYPES.AUTH_STATUS, 'status')) {
        loadTasks.push(this.checkAuthenticationStatus())
      }

      if (loadTasks.length > 0) {
        console.log(`[Index] 智能加载：需要刷新 ${loadTasks.length} 个数据源`)
        await Promise.allSettled(loadTasks)
      } else {
        console.log('[Index] 智能加载：所有数据都在缓存有效期内，无需刷新')
      }

      // 设置菜单图标颜色和行布局
      this.setMenuIconColors()
      this.setMenuRows()
    } catch (error) {
      console.warn('[Index] 智能加载数据失败:', error)
    }
  },

  // 获取用户缓存key（保留兼容性，实际由cacheManager内部处理）
  getUserCacheKey() {
    const state = stateManager.getState()
    return state.userInfo?.userId || 'anonymous'
  },

  // 检查用户变化并清除缓存
  checkUserChange() {
    const currentUserKey = this.getUserCacheKey()
    if (this.lastUserKey && this.lastUserKey !== currentUserKey) {
      // 用户变化，清除用户相关缓存
      cacheManager.clearUserCache()
    }
    this.lastUserKey = currentUserKey
  },

  // 刷新页面数据
  async refreshPageData() {
    const state = stateManager.getState()
    this.setData({
      isLogin: state.isLogin,
      userInfo: state.userInfo || null,
      ownerInfo: state.ownerInfo || null,
      isHouseAuth: state.isHouseAuth,
      houseInfo: state.houseInfo || null,
      communityInfo: state.communityInfo || this.data.communityInfo,
      visible: true,
      // 更新配置数据
      showBigCard: UIConfig.isBigCardEnabled(),
      enableMiniprogramTitle: UIConfig.isTitleEnabled(),
      miniprogramTitle: UIConfig.getTitle(),
      miniprogramSubtitle: UIConfig.getSubtitle()
    })
    wx.setNavigationBarTitle({
      title: (state.communityInfo && state.communityInfo.communityName) || this.data.communityInfo.communityName || '睦邻共治'
    })
  },

  // 刷新页面状态
  refreshPageState() {
    // 只更新页面数据状态，不控制loading和显示状态
    // loading和显示状态由初始化流程控制
  },

  // 检查并更新登录状态（简化版，不阻塞页面）
  async checkAndUpdateLoginStatus() {
    try {
      const token = wx.getStorageSync('token')
      const userInfo = wx.getStorageSync('wxUserInfo')

      if (!token || !userInfo?.nickName) {
        return this.checkLoginStatus()
      }

      // 更新状态管理器
      stateManager.setState({
        isLogin: true,
        userInfo
      })

      // 异步检查认证状态，不等待结果
      this.checkAuthenticationStatus().catch(error => {
        console.warn('[Index] 认证状态检查失败:', error)
      })

      return true

    } catch (error) {
      handleError(error, '登录状态检查')
      return this.checkLoginStatus()
    }
  },

  // 检查认证状态（带缓存）
  async checkAuthenticationStatus() {
    try {
      const state = stateManager.getState()
      const params = {
        ownerInfo: state.ownerInfo,
        userInfo: state.userInfo,
        tokenUser: state.tokenUser
      }

      const result = await dataService.checkAuthenticationStatus(params)

      if (result.success) {
        // 检查配置是否需要更新
        if (result.data.communityInfo && stateManager.checkConfigUpdate(result.data.communityInfo)) {
          console.log('[Index] 检测到配置更新，刷新配置')
        }

        // 更新状态管理器
        stateManager.setState(result.data)

        // 如果有新token，更新存储
        if (result.token) {
          wx.setStorageSync(STORAGE_KEYS.TOKEN, result.token)
        }

        await this.refreshPageData()
        this.updateWatermark()

        return result.result
      } else if (result.needLogin) {
        app.navigateToLogin()
        return false
      } else {
        console.warn('[Index] 认证状态检查失败:', result.error)
        return false
      }
    } catch (error) {
      console.warn('[Index] 认证状态检查异常:', error)
      return false
    }
  },



  // 获取新闻列表（按类型加载）
  async getNewsList(noticeType = '') {
    try {
      const result = await dataService.getNewsList(noticeType)

      if (result.success) {
        // 处理分类名称
        const list = result.data.map(item => ({
          ...item,
          categoryName: this.getCategoryName(item.noticeType || item.type || '1')
        }))

        this.setData({
          categoryNewsList: list,
          latestNotice: list[0] || null,
          hasMoreNews: result.hasMore
        })
      } else {
        console.warn('[News] 获取新闻列表失败:', result.error)
        // 新闻列表不是关键功能，失败不显示错误提示
      }
    } catch (error) {
      console.warn('[News] 获取新闻列表异常:', error)
    }
  },

  // 处理主卡片数据
  processMainCards(allMenus) {
    // 筛选top_show=1的菜单，按sort排序
    const topMenus = allMenus.filter(menu => menu.top_show === '1' || menu.top_show === 1)
                            .sort((a, b) => (a.sort || 0) - (b.sort || 0))
                            .slice(0, 2) // 最多取2个

    let mainCards = []

    // 根据top_show菜单数量决定显示策略
    if (topMenus.length === 2) {
      // 有2个top_show菜单，全部使用动态菜单
      mainCards = topMenus.map((menu, index) => ({
        ...menu,
        cardStyle: index === 0 ? 'repair-card' : 'contact-card', // 使用默认样式
        isDefault: false
      }))
    } else if (topMenus.length === 1) {
      // 有1个top_show菜单，使用1个动态+1个默认
      mainCards = [
        {
          ...topMenus[0],
          cardStyle: 'repair-card',
          isDefault: false
        },
        {
          ...DEFAULT_MAIN_CARDS[1], // 使用第二个默认菜单
          cardStyle: 'contact-card'
        }
      ]
    } else {
      // 没有top_show菜单，使用全部默认菜单
      mainCards = [...DEFAULT_MAIN_CARDS]
    }

    return mainCards
  },

  // 过滤掉已在主卡片中显示的菜单
  filterMainCardMenus(allMenus, mainCards) {
    // 获取主卡片中非默认菜单的nav_id
    const mainCardNavIds = mainCards
      .filter(card => !card.isDefault && card.nav_id)
      .map(card => card.nav_id)

    // 从菜单列表中过滤掉已在主卡片中显示的菜单
    return allMenus.filter(menu => !mainCardNavIds.includes(menu.nav_id))
  },

  // 获取菜单列表（带缓存）
  async getMenuList() {
    try {
      const result = await dataService.getMenuList('indexNav')

      if (result.success) {
        const menuData = result.data

        // 处理主卡片数据
        const mainCards = this.processMainCards(menuData)

        // 过滤掉已在主卡片中显示的菜单
        const filteredMenuData = this.filterMainCardMenus(menuData, mainCards)

        this.setData({
          menuList: filteredMenuData,
          dynamicMainCards: mainCards
        })

        // 显示功能容器
        if (this.uiManager) {
          this.uiManager.setFunctionContainerVisible(true)
        }

        // 设置菜单图标颜色和行布局
        this.setMenuIconColors()
        this.setMenuRows()
      } else {
        console.warn('[Menu] 获取菜单列表失败:', result.error)
        // 菜单列表失败时使用默认主卡片
        this.setData({
          menuList: [],
          dynamicMainCards: [...DEFAULT_MAIN_CARDS]
        })
        if (this.uiManager) {
          this.uiManager.setFunctionContainerVisible(true)
        }
      }
    } catch (error) {
      console.warn('[Menu] 获取菜单列表异常:', error)
      // 异常时使用默认主卡片
      this.setData({
        menuList: [],
        dynamicMainCards: [...DEFAULT_MAIN_CARDS]
      })
      if (this.uiManager) {
        this.uiManager.setFunctionContainerVisible(true)
      }
    }
  },

  // 获取通知类型字典（带缓存）
  async getNoticeTypes() {
    try {
      const result = await dataService.getNoticeTypes()

      if (result.success) {
        const allTypes = result.data

        this.setData({
          noticeTypes: allTypes,
          currentNoticeType: allTypes[0]?.dictValue || ''
        })

        // 获取数据后立即刷新公告列表
        this.getCategoryNews(allTypes[0]?.dictValue || '')
      } else {
        console.warn('[NoticeTypes] 获取通知类型失败:', result.error)
        // 失败时使用默认类型
        this.setData({
          noticeTypes: [{ dictValue: '', dictLabel: '全部', dictSort: 0 }],
          currentNoticeType: ''
        })
      }
    } catch (error) {
      console.warn('[NoticeTypes] 获取通知类型异常:', error)
      // 异常时使用默认类型
      this.setData({
        noticeTypes: [{ dictValue: '', dictLabel: '全部', dictSort: 0 }],
        currentNoticeType: ''
      })
    }
  },

  // 处理主卡片点击
  async handleMainCardTap(e) {
    await this.eventHandler.handleMainCardTap(e)
  },

  // 处理菜单点击
  async handleMenuTap(e) {
    await this.eventHandler.handleMenuTap(e)
  },


  goToNewsDetail(e) {
    if (!this.checkLoginStatus()) return

    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/notice/detail?id=${id}`
    })
  },

  goToRepair() {
    if (!this.checkLoginAndAuth()) return
    wx.navigateTo({ 
      url: '/pages/bx/bx' 
    })
  },

  goToComplaint() {
    if (!this.checkLoginAndAuth()) return
    wx.navigateTo({ 
      url: '/pages/complaint/complaint' 
    })
  },

  goServiceTel() {
    wx.navigateTo({
      url: '/pages/serviceTel/index'
    })
  },

  // 跳转到缴费账单页面
  goToPayment() {
    if (!this.checkLoginStatus()) return
    wx.navigateTo({
      url: '/pages/charge/bill/bill'
    })
  },

  // 跳转到缴费情况页面（别名方法）
  goToChargeBill() {
    this.goToPayment()
  },

  goToVisitor() {
    wx.navigateTo({
      url: '/pages/house/index?action=invite'
    })
  },

  goToOcInfo() {
    wx.navigateTo({ url: '/pages/ocinfo/ocinfo' })
  },

  goToPropertyPhone() {
    const phone = this.data.communityInfo?.servicePhone
    if (phone) {
      wx.makePhoneCall({ phoneNumber: phone })
    } else {
      wx.showToast({
        title: '暂无配置服务电话',
        icon: 'none'
      })
    }
  },

  // 显示即将推出提示
  showComingSoon(e) {
    let feature = e
    if (typeof e === 'object' && e.currentTarget && e.currentTarget.dataset) {
      feature = e.currentTarget.dataset.feature
    }
    wx.showToast({
      title: `${feature}功能即将推出`,
      icon: 'none'
    })
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      const systemInfo = getSystemInfoSyncCompat()
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight || 20
      })
    } catch (error) {
      console.warn('[SystemInfo] 获取系统信息失败:', error)
      this.setData({ statusBarHeight: 20 })
    }
  },

  // 设置菜单图标颜色
  setMenuIconColors() {
    const menuList = this.data.menuList.map((item, index) => ({
      ...item,
      iconBg: MENU_ICON_COLORS[index % MENU_ICON_COLORS.length]
    }))
    this.setData({ menuList })
  },

  // 设置菜单行布局
  setMenuRows() {
    let menuList = this.data.menuList

    // 获取配置的菜单行数，默认4行
    const menuRowsConfig = UIConfig.getMenuRows()
    const itemsPerRow = menuRowsConfig

    // 根据每行显示个数确定最大显示数量
    // 4个一排：最多显示2行（8个）
    // 3个一排：最多显示3行（9个）
    const maxRows = itemsPerRow === 3 ? 3 : 2
    const maxItems = itemsPerRow * maxRows

    // 如果菜单超过最大显示数量，只显示前面的菜单，最后一个位置显示"更多服务"
    if (menuList.length > maxItems) {
      const displayMenus = menuList.slice(0, maxItems - 1)
      // 添加"更多服务"项
      const moreServiceItem = {
        id: 'more-service',
        nav_name: '更多服务',
        icon_name: 'apps-o',
        iconBg: 'linear-gradient(135deg, #722ed1, #b37feb)',
        isMoreService: true
      }
      displayMenus.push(moreServiceItem)
      menuList = displayMenus
    }

    const rows = []
    for (let i = 0; i < menuList.length; i += itemsPerRow) {
      rows.push(menuList.slice(i, i + itemsPerRow))
    }
    this.setData({ menuRows: rows })
  },

  // 获取分类名称
  getCategoryName(type) {
    const noticeType = this.data.noticeTypes.find(item => item.dictValue === type)
    return noticeType ? noticeType.dictLabel : '通知公告'
  },

  // 切换通知类型
  switchNoticeType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ currentNoticeType: type })
    // 重新请求对应类型的数据
    this.getNewsList(type)
  },

  // 获取分类新闻（保留兼容性，但现在直接调用getNewsList）
  getCategoryNews(type) {
    this.getNewsList(type)
  },

  // 跳转到公告列表页
  goToNoticeList() {
    if (!this.checkLoginStatus()) return

    const currentType = this.data.currentNoticeType
    const url = currentType ? `/pages/notice/list?type=${currentType}` : '/pages/notice/list'

    wx.navigateTo({
      url: url
    })
  },

  // 更新水印
  updateWatermark() {
    try {
      watermarkManager.updateWatermark(this)
    } catch (error) {
      console.warn('[Watermark] 更新水印失败:', error)
    }
  },

  // 跳转到房屋列表
  goToHouseList() {
    wx.navigateTo({
      url: '/pages/house/index'
    })
  },

  // 自定义分享给朋友
  async onShareAppMessage() {
    const state = stateManager.getState()

    // 检查是否开启分享功能
    if (!ShareConfig.isEnabled()) {
      return null // 返回null禁用分享功能
    }

    const communityName = state.communityInfo?.communityName || '睦邻共治'
    const shareTitle = ShareConfig.getTitle()
    const shareDesc = ShareConfig.getDesc()
    const shareImage = ShareConfig.getImage()
    const userInfo = state.userInfo;
    const ownerInfo = state.ownerInfo;

    // 构建分享标题（是否显示分享者信息）
    let finalTitle = `${communityName} - ${shareTitle}`
    if (ShareConfig.isSharerInfoEnabled() && ownerInfo?.ownerName) {
      finalTitle += `（${ownerInfo.ownerName}推荐）`
    }

    // 先记录分享行为，获取shareId
    const shareId = await this.recordShare('app_message', finalTitle, shareDesc, shareImage)

    // 构建分享路径，使用shareId
    const sharePath = shareId ? `/pages/index/index?shareId=${shareId}` : '/pages/index/index'

    return {
      title: finalTitle,
      desc: shareDesc,
      path: sharePath,
      imageUrl: shareImage
    }
  },

  // 自定义分享到朋友圈
  async onShareTimeline() {
    const state = stateManager.getState()

    // 检查是否开启分享功能
    if (!ShareConfig.isEnabled()) {
      return null // 返回null禁用分享功能
    }

    const communityName = state.communityInfo?.communityName || '睦邻共治'
    const shareTitle = ShareConfig.getTitle()
    const shareDesc = ShareConfig.getDesc()
    const shareImage = ShareConfig.getImage()
    const userInfo = state.userInfo;
    const ownerInfo = state.ownerInfo;

    // 构建分享标题
    let finalTitle = `${communityName} - ${shareTitle}，${shareDesc}`
    if (ShareConfig.isSharerInfoEnabled() && ownerInfo?.ownerName) {
      finalTitle += `（${ownerInfo.ownerName}推荐）`
    }

    // 记录分享行为
    await this.recordShare('timeline', finalTitle, shareDesc, shareImage)

    return {
      title: finalTitle,
      query: `sharedBy=${userInfo?.userId || ''}&sharedName=${encodeURIComponent(userInfo?.nickName || '')}`,
      imageUrl: shareImage
    }
  },

  // 记录分享行为
  async recordShare(shareType, shareTitle, shareDesc, shareImage) {
    // 检查是否启用分享统计
    if (!ShareConfig.isStatisticsEnabled()) {
      return null
    }

    return new Promise((resolve) => {
      const requestData = {
        shareType: shareType,
        shareSource: 'index',
        shareTitle: shareTitle,
        shareDesc: shareDesc,
        sharePath: '',  // 先留空，后面会更新
        shareImage: shareImage
      }

      // 调用分享记录API
      app.request({
        url: '/api/wx/share/record',
        method: 'POST',
        data: requestData
      }).then((res) => {
        if (res.code === 0) {
          resolve(res.data.shareId)
        } else {
          resolve(null)
        }
      }).catch(() => {
        resolve(null)
      })
    })
  },

  // 检测分享来源
  checkShareSource() {
    try {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const options = currentPage.options || {}

      // 检查是否有shareId
      if (options.shareId) {
        // 记录分享访问
        setTimeout(() => {
          this.recordShareVisit(options.shareId)
        }, 1000) // 延迟1秒，确保页面加载完成
      }
    } catch (error) {
      // 静默处理错误
    }
  },

  // 记录分享访问
  recordShareVisit(shareId) {
    // 检查是否启用分享统计，默认启用
    if (!ShareConfig.isStatisticsEnabled() || !shareId) {
      return;
    }

    // 记录分享访问
    app.request({
      url: '/api/wx/share/visit',
      method: 'POST',
      data: { shareId: shareId }
    }).then(() => {
      // 静默处理成功
    }).catch(() => {
      // 静默处理失败
    })
  },

  // 配置更新回调
  onConfigUpdate() {
    console.log('[Index] 配置已更新，刷新页面状态')
    // 重新设置菜单行数
    this.setMenuRows()
    // 更新水印
    this.updateWatermark()
    // 刷新页面状态
    this.refreshPageState()
  },

  // 检查订阅状态
  async checkSubscribeStatus(subscribeStatus) {
    try {
      // 如果后端返回需要订阅
      if (subscribeStatus && subscribeStatus.needSubscribe) {


        // 延迟一段时间后弹出订阅请求，避免与其他弹窗冲突
        setTimeout(async () => {
          try {
            const result = await subscribeManager.smartSubscribeCheck(
              TEMPLATE_TYPES.PROPERTY_NOTICE,
              {
                title: '消息通知',
                content: '订阅物业通知后，您将及时收到重要的物业公告和服务信息',
                autoRequest: true
              }
            )

            if (result) {
              wx.showToast({
                title: '订阅成功',
                icon: 'success',
                duration: 2000
              })
            }
          } catch (error) {
            // 订阅检查失败，静默处理
          }
        }, 2000) // 延迟2秒
      }
    } catch (error) {
      console.warn('[Index] 检查订阅状态失败:', error)
    }
  },

  // 检查是否需要显示添加到桌面引导
  checkDesktopGuide() {
    try {
      // 检查本地存储中是否已经显示过引导
      const hasShownGuide = wx.getStorageSync(STORAGE_KEYS.DESKTOP_GUIDE_SHOWN)

      if (!hasShownGuide) {
        // 延迟3秒后显示引导，确保页面完全加载
        setTimeout(() => {
          // 再次检查页面是否还在显示状态
          if (this.data.contentVisible) {
            this.setData({
              showDesktopGuide: true
            })
          }
        }, 3000)
      }
    } catch (error) {
      console.warn('[Index] 检查桌面引导失败:', error)
    }
  },

  // 隐藏桌面引导弹窗
  hideDesktopGuide() {
    this.setData({
      showDesktopGuide: false
    })
  },

  // 确认桌面引导（不再提示）
  confirmDesktopGuide() {
    try {
      // 设置本地存储标记，表示已经显示过引导
      wx.setStorageSync(STORAGE_KEYS.DESKTOP_GUIDE_SHOWN, true)

      // 隐藏弹窗
      this.setData({
        showDesktopGuide: false
      })

      wx.showToast({
        title: '已设置不再提示',
        icon: 'success',
        duration: 2000
      })
    } catch (error) {
      console.warn('[Index] 设置桌面引导标记失败:', error)
      // 即使设置失败也要隐藏弹窗
      this.setData({
        showDesktopGuide: false
      })
    }
  },

  // 防止弹窗内容区域点击时关闭弹窗
  preventClose() {
    // 空方法，阻止事件冒泡
  },

  // 页面卸载时清理资源
  onUnload() {
    // 重置onShow运行标记
    this.isOnShowRunning = false

    if (this.uiManager) {
      this.uiManager.destroy()
      this.uiManager = null
    }

    if (this.eventHandler) {
      this.eventHandler = null
    }
  },

  // 页面隐藏时重置标记
  onHide() {
    // 重置onShow运行标记，确保下次显示时能正常执行
    this.isOnShowRunning = false
  }

})

