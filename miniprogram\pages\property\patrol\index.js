// 巡检记录页面
import { handlePropertyPageShow } from '../../../utils/pageUtils.js'

Page({
  data: {
    title: '巡检记录',
    patrolList: [],
    loading: false
  },

  onLoad() {
    console.log('巡检记录页面加载')
    wx.setNavigationBarTitle({
      title: this.data.title
    })
  },

  onShow() {
    handlePropertyPageShow(this, this.loadPatrolList)
  },

  // 加载巡检记录
  loadPatrolList() {
    this.setData({ loading: true })
    
    // TODO: 实现巡检记录加载
    setTimeout(() => {
      this.setData({ 
        loading: false,
        patrolList: [
          { id: 1, location: '小区大门', time: '2024-01-15 09:00', status: '正常' },
          { id: 2, location: '地下车库', time: '2024-01-15 10:30', status: '异常' }
        ]
      })
    }, 1000)
  },

  // 查看巡检详情
  viewPatrolDetail(e) {
    const patrolId = e.currentTarget.dataset.id
    console.log('查看巡检详情:', patrolId)
    // TODO: 跳转到巡检详情页面
  }
})
