// 死循环检测器
// 用于实时监控可能导致死循环的代码模式

class DeadLoopDetector {
  constructor() {
    this.callCounts = new Map() // 方法调用计数
    this.timers = new Set() // 活跃的定时器
    this.intervals = new Set() // 活跃的间隔器
    this.monitoring = false
    this.maxCallsPerSecond = 100 // 每秒最大调用次数
    this.checkInterval = 1000 // 检查间隔（毫秒）
    this.monitorTimer = null
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    if (this.monitoring) {
      console.log('[DeadLoopDetector] 已在监控中')
      return
    }

    this.monitoring = true
    console.log('[DeadLoopDetector] 开始监控死循环...')

    // 拦截setTimeout
    this.interceptSetTimeout()
    
    // 拦截setInterval
    this.interceptSetInterval()
    
    // 拦截setData
    this.interceptSetData()
    
    // 拦截Promise
    this.interceptPromise()

    // 定期检查调用频率
    this.monitorTimer = setInterval(() => {
      this.checkCallFrequency()
    }, this.checkInterval)
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (!this.monitoring) {
      return
    }

    this.monitoring = false
    console.log('[DeadLoopDetector] 停止监控')

    if (this.monitorTimer) {
      clearInterval(this.monitorTimer)
      this.monitorTimer = null
    }

    // 恢复原始方法
    this.restoreOriginalMethods()
  }

  /**
   * 拦截setTimeout
   */
  interceptSetTimeout() {
    const originalSetTimeout = setTimeout
    const detector = this

    setTimeout = function(callback, delay, ...args) {
      const timerId = originalSetTimeout(() => {
        detector.timers.delete(timerId)
        detector.recordCall('setTimeout_callback')
        try {
          callback.apply(this, args)
        } catch (error) {
          console.error('[DeadLoopDetector] setTimeout回调错误:', error)
        }
      }, delay)

      detector.timers.add(timerId)
      detector.recordCall('setTimeout')
      
      if (detector.timers.size > 50) {
        console.warn(`[DeadLoopDetector] 活跃定时器过多: ${detector.timers.size}`)
      }

      return timerId
    }
  }

  /**
   * 拦截setInterval
   */
  interceptSetInterval() {
    const originalSetInterval = setInterval
    const detector = this

    setInterval = function(callback, delay, ...args) {
      const intervalId = originalSetInterval(() => {
        detector.recordCall('setInterval_callback')
        try {
          callback.apply(this, args)
        } catch (error) {
          console.error('[DeadLoopDetector] setInterval回调错误:', error)
        }
      }, delay)

      detector.intervals.add(intervalId)
      detector.recordCall('setInterval')
      
      if (detector.intervals.size > 10) {
        console.warn(`[DeadLoopDetector] 活跃间隔器过多: ${detector.intervals.size}`)
      }

      return intervalId
    }
  }

  /**
   * 拦截setData
   */
  interceptSetData() {
    // 拦截页面的setData
    const pages = getCurrentPages()
    pages.forEach(page => {
      if (page.setData && !page._originalSetData) {
        page._originalSetData = page.setData
        const detector = this

        page.setData = function(data, callback) {
          detector.recordCall('setData')
          
          // 检查setData调用频率
          const setDataCount = detector.callCounts.get('setData') || 0
          if (setDataCount > 20) {
            console.warn(`[DeadLoopDetector] setData调用频繁: ${setDataCount}次/秒`)
          }

          return page._originalSetData.call(this, data, callback)
        }
      }
    })
  }

  /**
   * 拦截Promise
   */
  interceptPromise() {
    // 监控Promise.all和Promise.allSettled
    const originalPromiseAll = Promise.all
    const originalPromiseAllSettled = Promise.allSettled
    const detector = this

    Promise.all = function(promises) {
      detector.recordCall('Promise.all')
      return originalPromiseAll.call(this, promises)
    }

    Promise.allSettled = function(promises) {
      detector.recordCall('Promise.allSettled')
      return originalPromiseAllSettled.call(this, promises)
    }
  }

  /**
   * 记录方法调用
   */
  recordCall(methodName) {
    const count = this.callCounts.get(methodName) || 0
    this.callCounts.set(methodName, count + 1)
  }

  /**
   * 检查调用频率
   */
  checkCallFrequency() {
    const suspiciousMethods = []

    this.callCounts.forEach((count, method) => {
      if (count > this.maxCallsPerSecond) {
        suspiciousMethods.push({ method, count })
      }
    })

    if (suspiciousMethods.length > 0) {
      console.warn('[DeadLoopDetector] 检测到可疑的高频调用:')
      suspiciousMethods.forEach(({ method, count }) => {
        console.warn(`  ${method}: ${count}次/秒`)
      })
    }

    // 重置计数
    this.callCounts.clear()
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      monitoring: this.monitoring,
      activeTimers: this.timers.size,
      activeIntervals: this.intervals.size,
      recentCalls: Object.fromEntries(this.callCounts)
    }
  }

  /**
   * 强制清理所有定时器
   */
  forceCleanup() {
    console.log('[DeadLoopDetector] 强制清理所有定时器...')
    
    // 清理setTimeout
    this.timers.forEach(timerId => {
      try {
        clearTimeout(timerId)
      } catch (error) {
        console.warn('[DeadLoopDetector] 清理定时器失败:', error)
      }
    })
    this.timers.clear()

    // 清理setInterval
    this.intervals.forEach(intervalId => {
      try {
        clearInterval(intervalId)
      } catch (error) {
        console.warn('[DeadLoopDetector] 清理间隔器失败:', error)
      }
    })
    this.intervals.clear()

    console.log('[DeadLoopDetector] 强制清理完成')
  }

  /**
   * 恢复原始方法
   */
  restoreOriginalMethods() {
    // 这里可以恢复被拦截的方法，但在小程序环境中比较复杂
    // 暂时跳过，重启小程序即可恢复
  }
}

// 创建全局实例
const deadLoopDetector = new DeadLoopDetector()

// 导出检测器和便捷方法
export default deadLoopDetector

export function startDeadLoopDetection() {
  deadLoopDetector.startMonitoring()
  
  // 5秒后输出状态报告
  setTimeout(() => {
    const status = deadLoopDetector.getStatus()
    console.log('[DeadLoopDetector] 状态报告:', status)
  }, 5000)
}

export function stopDeadLoopDetection() {
  deadLoopDetector.stopMonitoring()
}

export function getDetectorStatus() {
  return deadLoopDetector.getStatus()
}

export function forceCleanupTimers() {
  deadLoopDetector.forceCleanup()
}

// 紧急停止函数
export function emergencyStop() {
  console.log('[DeadLoopDetector] 🚨 紧急停止所有可能的死循环!')
  
  // 停止监控
  deadLoopDetector.stopMonitoring()
  
  // 强制清理定时器
  deadLoopDetector.forceCleanup()
  
  // 清理TokenManager
  try {
    const tokenManager = require('../utils/tokenManager.js').default
    if (tokenManager && typeof tokenManager.cleanup === 'function') {
      tokenManager.cleanup()
    }
  } catch (error) {
    console.error('[DeadLoopDetector] 清理TokenManager失败:', error)
  }
  
  // 清理RequestManager
  try {
    const requestManager = require('../utils/requestManager.js').default
    if (requestManager && typeof requestManager.destroy === 'function') {
      requestManager.destroy()
    }
  } catch (error) {
    console.error('[DeadLoopDetector] 清理RequestManager失败:', error)
  }
  
  console.log('[DeadLoopDetector] 🎯 紧急停止完成!')
}
