/**
 * LoginService TabBar修复验证测试
 * 验证loginService.js中的initTabBarConfig方法修复是否有效
 */

/**
 * 测试initTabBarConfig方法是否正常工作
 */
export function testInitTabBarConfig() {
  console.log('\n=== LoginService TabBar修复测试 ===')

  try {
    // 模拟登录服务
    const { getLoginService } = require('../services/loginService.js')
    const loginService = getLoginService()

    console.log('1. 测试initTabBarConfig方法存在性...')
    if (typeof loginService.initTabBarConfig === 'function') {
      console.log('✓ initTabBarConfig方法存在')
    } else {
      console.error('✗ initTabBarConfig方法不存在')
      return false
    }

    console.log('2. 测试物业用户TabBar配置更新...')

    // 模拟getCurrentPages
    const originalGetCurrentPages = global.getCurrentPages
    global.getCurrentPages = () => [{
      getTabBar: () => ({
        updateTabBarConfig: (userType) => {
          console.log(`✓ TabBar updateTabBarConfig被调用，userType=${userType}`)
          if (userType === '2') {
            console.log('✓ 物业用户类型传递正确')
          } else {
            console.warn(`⚠️ 期望userType=2，实际=${userType}`)
          }
        },
        updateTabBarByUserType: () => {
          console.log('✓ TabBar updateTabBarByUserType被调用(兼容模式)')
        }
      })
    }]

    // 测试业主用户
    console.log('3. 测试业主用户(userType=1)...')
    loginService.initTabBarConfig('1')

    // 测试物业用户
    console.log('4. 测试物业用户(userType=2)...')
    loginService.initTabBarConfig('2')

    // 等待延迟执行
    setTimeout(() => {
      console.log('✓ initTabBarConfig调用成功，无错误')

      // 恢复原始方法
      global.getCurrentPages = originalGetCurrentPages

      console.log('LoginService TabBar修复测试通过 ✓')
    }, 150)

    return true

  } catch (error) {
    console.error('LoginService TabBar修复测试失败:', error)
    return false
  }
}

/**
 * 测试TabBar组件的init方法
 */
export function testTabBarInitMethod() {
  console.log('\n=== TabBar init方法测试 ===')
  
  try {
    console.log('1. 检查TabBar组件是否有init方法...')
    
    // 模拟TabBar组件
    const pages = getCurrentPages()
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      
      if (currentPage && typeof currentPage.getTabBar === 'function') {
        const tabBar = currentPage.getTabBar()
        
        if (tabBar) {
          if (typeof tabBar.init === 'function') {
            console.log('✓ TabBar组件有init方法')
            
            // 测试调用
            tabBar.init()
            console.log('✓ TabBar init方法调用成功')
            
            return true
          } else {
            console.error('✗ TabBar组件没有init方法')
            return false
          }
        } else {
          console.warn('当前页面没有TabBar组件')
          return true // 不是错误，只是当前页面不是TabBar页面
        }
      } else {
        console.warn('当前页面不支持getTabBar')
        return true
      }
    } else {
      console.warn('没有当前页面')
      return true
    }
    
  } catch (error) {
    console.error('TabBar init方法测试失败:', error)
    return false
  }
}

/**
 * 测试物业页面TabBar初始化
 */
export function testPropertyPageTabBarInit() {
  console.log('\n=== 物业页面TabBar初始化测试 ===')

  try {
    // 模拟物业页面工具函数
    const { handlePropertyPageShow } = require('../utils/pageUtils.js')

    console.log('1. 测试handlePropertyPageShow方法存在性...')
    if (typeof handlePropertyPageShow === 'function') {
      console.log('✓ handlePropertyPageShow方法存在')
    } else {
      console.error('✗ handlePropertyPageShow方法不存在')
      return false
    }

    console.log('2. 测试物业页面TabBar初始化...')

    // 模拟页面实例
    const mockPageInstance = {
      getTabBar: () => ({
        init: () => {
          console.log('✓ TabBar init方法被调用')
        },
        setSelected: (index) => {
          console.log(`✓ TabBar setSelected被调用，index=${index}`)
        }
      })
    }

    // 模拟数据加载函数
    const mockLoadDataFn = function() {
      console.log('✓ 数据加载函数被调用')
    }

    // 调用物业页面处理函数
    handlePropertyPageShow(mockPageInstance, 0, mockLoadDataFn)

    console.log('物业页面TabBar初始化测试通过 ✓')
    return true

  } catch (error) {
    console.error('物业页面TabBar初始化测试失败:', error)
    return false
  }
}

/**
 * 运行所有修复验证测试
 */
export function runLoginServiceTabBarFixTests() {
  console.log('开始LoginService TabBar修复验证测试...')

  const results = []

  results.push(testInitTabBarConfig())
  results.push(testTabBarInitMethod())
  results.push(testPropertyPageTabBarInit())

  const passedCount = results.filter(r => r).length
  const totalCount = results.length

  console.log(`\n测试完成: ${passedCount}/${totalCount} 通过`)

  if (passedCount === totalCount) {
    console.log('🎉 所有LoginService TabBar修复测试通过！')
    return true
  } else {
    console.warn('⚠️ 部分测试失败，请检查修复代码')
    return false
  }
}

// 导出测试方法
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testInitTabBarConfig,
    testTabBarInitMethod,
    testPropertyPageTabBarInit,
    runLoginServiceTabBarFixTests
  }
}
