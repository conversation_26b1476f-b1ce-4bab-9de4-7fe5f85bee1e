// 性能修复测试
// 用于验证死循环问题是否已解决

/**
 * 测试TokenManager的重试机制
 */
export function testTokenManagerRetry() {
  console.log('\n=== TokenManager重试机制测试 ===')
  
  try {
    const tokenManager = require('../utils/tokenManager.js').default
    
    console.log('1. 检查TokenManager实例...')
    if (!tokenManager) {
      console.error('TokenManager实例不存在')
      return false
    }
    
    console.log('2. 检查重试限制配置...')
    console.log(`最大重试次数: ${tokenManager.maxRetryAttempts}`)
    console.log(`当前重试计数: ${tokenManager.retryCount}`)
    
    console.log('3. 检查定时器状态...')
    console.log(`检查定时器: ${tokenManager.checkTimer ? '运行中' : '未运行'}`)
    console.log(`重试定时器: ${tokenManager.retryTimer ? '运行中' : '未运行'}`)
    
    console.log('TokenManager重试机制测试通过 ✓')
    return true
    
  } catch (error) {
    console.error('TokenManager重试机制测试失败:', error)
    return false
  }
}

/**
 * 测试RequestManager的资源清理
 */
export function testRequestManagerCleanup() {
  console.log('\n=== RequestManager资源清理测试 ===')
  
  try {
    const requestManager = require('../utils/requestManager.js').default
    
    console.log('1. 检查RequestManager实例...')
    if (!requestManager) {
      console.error('RequestManager实例不存在')
      return false
    }
    
    console.log('2. 检查清理定时器状态...')
    console.log(`清理定时器: ${requestManager._cleanupTimer ? '运行中' : '未运行'}`)
    
    console.log('3. 测试destroy方法...')
    if (typeof requestManager.destroy === 'function') {
      console.log('destroy方法存在 ✓')
    } else {
      console.error('destroy方法不存在')
      return false
    }
    
    console.log('RequestManager资源清理测试通过 ✓')
    return true
    
  } catch (error) {
    console.error('RequestManager资源清理测试失败:', error)
    return false
  }
}

/**
 * 测试App生命周期清理
 */
export function testAppLifecycleCleanup() {
  console.log('\n=== App生命周期清理测试 ===')
  
  try {
    const app = getApp()
    
    console.log('1. 检查App实例...')
    if (!app) {
      console.error('App实例不存在')
      return false
    }
    
    console.log('2. 检查onUnload方法...')
    if (typeof app.onUnload === 'function') {
      console.log('onUnload方法存在 ✓')
    } else {
      console.error('onUnload方法不存在')
      return false
    }
    
    console.log('App生命周期清理测试通过 ✓')
    return true
    
  } catch (error) {
    console.error('App生命周期清理测试失败:', error)
    return false
  }
}

/**
 * 模拟网络错误测试
 */
export function testNetworkErrorHandling() {
  console.log('\n=== 网络错误处理测试 ===')
  
  try {
    const tokenManager = require('../utils/tokenManager.js').default
    
    console.log('1. 检查网络错误判断方法...')
    if (typeof tokenManager.isNetworkError === 'function') {
      console.log('isNetworkError方法存在 ✓')
    } else {
      console.error('isNetworkError方法不存在')
      return false
    }
    
    console.log('2. 测试网络错误判断...')
    const networkError = { errMsg: 'request:fail timeout' }
    const isNetworkErr = tokenManager.isNetworkError(networkError)
    console.log(`网络错误判断结果: ${isNetworkErr}`)
    
    console.log('网络错误处理测试通过 ✓')
    return true
    
  } catch (error) {
    console.error('网络错误处理测试失败:', error)
    return false
  }
}

/**
 * 运行所有性能修复测试
 */
export function runAllPerformanceFixTests() {
  console.log('开始性能修复验证测试...')
  
  const results = []
  
  results.push(testTokenManagerRetry())
  results.push(testRequestManagerCleanup())
  results.push(testAppLifecycleCleanup())
  results.push(testNetworkErrorHandling())
  
  const passedCount = results.filter(r => r).length
  const totalCount = results.length
  
  console.log(`\n测试完成: ${passedCount}/${totalCount} 通过`)
  
  if (passedCount === totalCount) {
    console.log('🎉 所有性能修复测试通过！')
    return true
  } else {
    console.warn('⚠️ 部分测试失败，请检查相关代码')
    return false
  }
}

// 如果直接运行此文件，执行所有测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testTokenManagerRetry,
    testRequestManagerCleanup,
    testAppLifecycleCleanup,
    testNetworkErrorHandling,
    runAllPerformanceFixTests
  }
} else {
  // 在小程序环境中可以手动调用
  // runAllPerformanceFixTests()
}
