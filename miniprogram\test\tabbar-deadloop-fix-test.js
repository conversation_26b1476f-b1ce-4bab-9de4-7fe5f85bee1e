// TabBar死循环修复测试
// 用于验证TabBar相关的死循环问题是否已解决

/**
 * 测试TabBar防重复更新机制
 */
export function testTabBarAntiDuplicate() {
  console.log('\n=== TabBar防重复更新测试 ===')
  
  try {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    
    if (!currentPage || typeof currentPage.getTabBar !== 'function') {
      console.warn('当前页面不支持TabBar')
      return true
    }
    
    const tabBar = currentPage.getTabBar()
    if (!tabBar) {
      console.warn('TabBar组件不存在')
      return true
    }
    
    console.log('1. 检查TabBar防重复更新状态...')
    console.log(`isUpdating状态: ${tabBar.data.isUpdating}`)
    console.log(`当前用户类型: ${tabBar.data.currentUserType}`)
    console.log(`当前选中索引: ${tabBar.data.selected}`)
    
    console.log('2. 测试重复设置相同选中状态...')
    const currentSelected = tabBar.data.selected
    
    // 连续设置相同的选中状态，应该被防重复机制拦截
    for (let i = 0; i < 5; i++) {
      tabBar.setSelected(currentSelected)
    }
    
    console.log('3. 测试重复更新相同用户类型...')
    const currentUserType = tabBar.data.currentUserType
    
    // 连续更新相同的用户类型，应该被防重复机制拦截
    for (let i = 0; i < 5; i++) {
      tabBar.updateTabBarConfig(currentUserType)
    }
    
    console.log('TabBar防重复更新测试通过 ✓')
    return true
    
  } catch (error) {
    console.error('TabBar防重复更新测试失败:', error)
    return false
  }
}

/**
 * 测试TabBar方法存在性
 */
export function testTabBarMethods() {
  console.log('\n=== TabBar方法存在性测试 ===')
  
  try {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    
    if (!currentPage || typeof currentPage.getTabBar !== 'function') {
      console.warn('当前页面不支持TabBar')
      return true
    }
    
    const tabBar = currentPage.getTabBar()
    if (!tabBar) {
      console.warn('TabBar组件不存在')
      return true
    }
    
    console.log('1. 检查必需方法...')
    const requiredMethods = [
      'setSelected',
      'updateTabBarConfig',
      'updateTabBarByUserType',
      'initTabBar',
      'getIndexByPath'
    ]
    
    for (const method of requiredMethods) {
      if (typeof tabBar[method] === 'function') {
        console.log(`✓ ${method} 方法存在`)
      } else {
        console.error(`✗ ${method} 方法不存在`)
        return false
      }
    }
    
    console.log('2. 测试getIndexByPath方法...')
    const testPaths = [
      '/pages/index/index',
      '/pages/service/index',
      '/pages/finance/finance',
      '/pages/mine/index',
      '/pages/nonexistent/page'
    ]
    
    for (const path of testPaths) {
      const index = tabBar.getIndexByPath(path)
      console.log(`路径 ${path} 对应索引: ${index}`)
    }
    
    console.log('TabBar方法存在性测试通过 ✓')
    return true
    
  } catch (error) {
    console.error('TabBar方法存在性测试失败:', error)
    return false
  }
}

/**
 * 测试TabBar并发保护机制
 */
export function testTabBarConcurrencyProtection() {
  console.log('\n=== TabBar并发保护测试 ===')
  
  try {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    
    if (!currentPage || typeof currentPage.getTabBar !== 'function') {
      console.warn('当前页面不支持TabBar')
      return true
    }
    
    const tabBar = currentPage.getTabBar()
    if (!tabBar) {
      console.warn('TabBar组件不存在')
      return true
    }
    
    console.log('1. 测试并发初始化保护...')
    
    // 模拟并发调用initTabBar
    const promises = []
    for (let i = 0; i < 3; i++) {
      promises.push(new Promise(resolve => {
        setTimeout(() => {
          tabBar.initTabBar()
          resolve()
        }, i * 10)
      }))
    }
    
    Promise.all(promises).then(() => {
      console.log('并发初始化测试完成')
    })
    
    console.log('2. 测试并发配置更新保护...')
    
    // 模拟并发调用updateTabBarConfig
    for (let i = 0; i < 3; i++) {
      setTimeout(() => {
        tabBar.updateTabBarConfig('1')
      }, i * 10)
    }
    
    console.log('TabBar并发保护测试通过 ✓')
    return true
    
  } catch (error) {
    console.error('TabBar并发保护测试失败:', error)
    return false
  }
}

/**
 * 测试页面TabBar调用优化
 */
export function testPageTabBarOptimization() {
  console.log('\n=== 页面TabBar调用优化测试 ===')
  
  try {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    
    if (!currentPage || typeof currentPage.getTabBar !== 'function') {
      console.warn('当前页面不支持TabBar')
      return true
    }
    
    const tabBar = currentPage.getTabBar()
    if (!tabBar) {
      console.warn('TabBar组件不存在')
      return true
    }
    
    console.log('1. 检查页面是否使用setSelected而非setData...')
    
    // 检查当前页面的onShow方法
    const pageRoute = currentPage.route
    console.log(`当前页面路径: ${pageRoute}`)
    
    // 测试setSelected方法的性能
    const startTime = Date.now()
    for (let i = 0; i < 10; i++) {
      tabBar.setSelected(0)
    }
    const elapsed = Date.now() - startTime
    
    console.log(`setSelected方法10次调用耗时: ${elapsed}ms`)
    
    if (elapsed < 100) {
      console.log('setSelected方法性能正常 ✓')
    } else {
      console.warn('setSelected方法性能可能有问题')
    }
    
    console.log('页面TabBar调用优化测试通过 ✓')
    return true
    
  } catch (error) {
    console.error('页面TabBar调用优化测试失败:', error)
    return false
  }
}

/**
 * 运行所有TabBar死循环修复测试
 */
export function runAllTabBarDeadLoopFixTests() {
  console.log('开始TabBar死循环修复验证测试...')
  
  const results = []
  
  results.push(testTabBarAntiDuplicate())
  results.push(testTabBarMethods())
  results.push(testTabBarConcurrencyProtection())
  results.push(testPageTabBarOptimization())
  
  const passedCount = results.filter(r => r).length
  const totalCount = results.length
  
  console.log(`\n测试完成: ${passedCount}/${totalCount} 通过`)
  
  if (passedCount === totalCount) {
    console.log('🎉 所有TabBar死循环修复测试通过！')
    return true
  } else {
    console.warn('⚠️ 部分测试失败，请检查相关代码')
    return false
  }
}

// 如果直接运行此文件，执行所有测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testTabBarAntiDuplicate,
    testTabBarMethods,
    testTabBarConcurrencyProtection,
    testPageTabBarOptimization,
    runAllTabBarDeadLoopFixTests
  }
} else {
  // 在小程序环境中可以手动调用
  // runAllTabBarDeadLoopFixTests()
}
