// TabBar性能测试
import { getTabBarManager } from '../utils/tabBarManager.js'

/**
 * 测试防重复调用机制
 */
export function testAntiDuplicateCall() {
  console.log('\n=== 防重复调用测试 ===')
  
  try {
    const tabBarManager = getTabBarManager()
    const startTime = Date.now()
    
    console.log('1. 快速连续调用updateUserType...')
    
    // 快速连续调用，测试防重复机制
    for (let i = 0; i < 20; i++) {
      tabBarManager.updateUserType(i % 2 === 0 ? '1' : '2')
    }
    
    const elapsed = Date.now() - startTime
    console.log(`快速调用20次耗时: ${elapsed}ms`)
    
    if (elapsed < 1000) {
      console.log('防重复调用机制正常 ✓')
    } else {
      console.warn('调用耗时过长，可能存在性能问题')
    }
    
  } catch (error) {
    console.error('防重复调用测试失败:', error)
  }
}

/**
 * 测试强制刷新性能
 */
export function testForceRefreshPerformance() {
  console.log('\n=== 强制刷新性能测试 ===')
  
  try {
    const tabBarManager = getTabBarManager()
    
    console.log('1. 测试连续强制刷新...')
    const startTime = Date.now()
    
    // 连续调用强制刷新
    for (let i = 0; i < 5; i++) {
      tabBarManager.forceRefresh()
    }
    
    const elapsed = Date.now() - startTime
    console.log(`连续强制刷新5次耗时: ${elapsed}ms`)
    
    if (elapsed < 500) {
      console.log('强制刷新性能正常 ✓')
    } else {
      console.warn('强制刷新耗时过长')
    }
    
  } catch (error) {
    console.error('强制刷新性能测试失败:', error)
  }
}

/**
 * 测试内存泄漏
 */
export function testMemoryLeak() {
  console.log('\n=== 内存泄漏测试 ===')
  
  try {
    console.log('1. 创建多个TabBar管理器实例...')
    
    const managers = []
    for (let i = 0; i < 10; i++) {
      managers.push(getTabBarManager())
    }
    
    // 验证单例模式
    const allSame = managers.every(manager => manager === managers[0])
    
    if (allSame) {
      console.log('单例模式正常，无内存泄漏风险 ✓')
    } else {
      console.warn('检测到多个实例，可能存在内存泄漏')
    }
    
  } catch (error) {
    console.error('内存泄漏测试失败:', error)
  }
}

/**
 * 测试错误处理
 */
export function testErrorHandling() {
  console.log('\n=== 错误处理测试 ===')
  
  try {
    const tabBarManager = getTabBarManager()
    
    console.log('1. 测试无效用户类型...')
    tabBarManager.updateUserType('invalid')
    tabBarManager.updateUserType(null)
    tabBarManager.updateUserType(undefined)
    
    console.log('2. 测试异常情况下的强制刷新...')
    
    // 模拟异常情况
    const originalGetCurrentPages = global.getCurrentPages
    global.getCurrentPages = null
    
    tabBarManager.forceRefresh()
    
    // 恢复
    global.getCurrentPages = originalGetCurrentPages
    
    console.log('错误处理测试完成 ✓')
    
  } catch (error) {
    console.error('错误处理测试失败:', error)
  }
}

/**
 * 运行所有性能测试
 */
export function runAllPerformanceTests() {
  console.log('开始TabBar性能测试...')
  
  testAntiDuplicateCall()
  testForceRefreshPerformance()
  testMemoryLeak()
  testErrorHandling()
  
  console.log('\n所有性能测试完成！')
}

// 如果直接运行此文件，执行所有测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testAntiDuplicateCall,
    testForceRefreshPerformance,
    testMemoryLeak,
    testErrorHandling,
    runAllPerformanceTests
  }
} else {
  // 在小程序环境中可以手动调用
  // runAllPerformanceTests()
}
