/**
 * 页面工具类 - 提供公共的页面功能（重构版，按照微信官方最佳实践）
 */

/**
 * 物业页面通用的onShow处理
 * @param {Object} pageInstance 页面实例
 * @param {Function} loadDataFn 加载数据的函数
 */
export function handlePropertyPageShow(pageInstance, loadDataFn) {
  // 确保TabBar配置正确（物业用户）
  if (typeof pageInstance.getTabBar === 'function' && pageInstance.getTabBar()) {
    const tabBar = pageInstance.getTabBar()

    // 切换到物业菜单
    if (typeof tabBar.toggleMenu === 'function') {
      tabBar.toggleMenu('2')
    }

    // 初始化选中状态
    if (typeof tabBar.init === 'function') {
      tabBar.init()
    }
  }

  // 加载页面数据
  if (typeof loadDataFn === 'function') {
    loadDataFn.call(pageInstance)
  }
}

/**
 * 业主页面通用的onShow处理
 * @param {Object} pageInstance 页面实例
 * @param {Function} loadDataFn 加载数据的函数
 */
export function handleOwnerPageShow(pageInstance, loadDataFn) {
  // 确保TabBar配置正确（业主用户）
  if (typeof pageInstance.getTabBar === 'function' && pageInstance.getTabBar()) {
    const tabBar = pageInstance.getTabBar()

    // 切换到业主菜单
    if (typeof tabBar.toggleMenu === 'function') {
      tabBar.toggleMenu('1')
    }

    // 初始化选中状态
    if (typeof tabBar.init === 'function') {
      tabBar.init()
    }
  }

  // 加载页面数据
  if (typeof loadDataFn === 'function') {
    loadDataFn.call(pageInstance)
  }
}

/**
 * 获取当前用户类型
 * @returns {string} 用户类型 '1'=业主, '2'=物业
 */
export function getCurrentUserType() {
  try {
    const app = getApp()
    if (app && app.globalData && app.globalData.stateManager) {
      const state = app.globalData.stateManager.getState()
      return state.userType || '1'
    }
  } catch (error) {
    console.error('获取用户类型失败:', error)
  }
  return '1' // 默认业主
}

/**
 * 检查是否为物业用户
 * @returns {boolean}
 */
export function isPropertyUser() {
  return getCurrentUserType() === '2'
}

/**
 * 检查是否为业主用户
 * @returns {boolean}
 */
export function isOwnerUser() {
  return getCurrentUserType() === '1'
}
